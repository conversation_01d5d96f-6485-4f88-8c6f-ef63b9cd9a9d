import { hasuraQuery } from "@/utils/db";

export interface SupportFAQ {
  id: number;
  question: string;
  answer: string;
  is_active: boolean;
  display_order: number;
  created_at: string;
  updated_at: string;
}

interface SupportFAQResponse {
  ptvuser_support_faq: SupportFAQ[];
}

/**
 * Fetch support FAQs from the database
 */
export async function getSupportFAQs(
  activeOnly: boolean = true
): Promise<SupportFAQ[]> {
  try {
    let whereClause = "";

    if (activeOnly) {
      whereClause = "where: {is_active: {_eq: true}}";
    }

    const query = `
      query GetSupportFAQ {
        ptvuser_support_faq(
          ${whereClause}
          order_by: [
            {display_order: asc},
            {created_at: asc}
          ]
        ) {
          id
          question
          answer
          is_active
          display_order
          created_at
          updated_at
        }
      }
    `;

    const result = await hasuraQuery<SupportFAQResponse>(query);
    return result.ptvuser_support_faq;
  } catch (error) {
    console.error(
      "Eroare la obținerea intrebărilor frecvente de suport:",
      error
    );

    return [];
  }
}

/**
 * Get a specific FAQ by ID
 */
export async function getSupportFAQById(
  id: number
): Promise<SupportFAQ | null> {
  try {
    const query = `
      query GetSupportFAQById($id: Int!) {
        support_faq_by_pk(id: $id) {
          id
          question
          answer
          is_active
          display_order
          created_at
          updated_at
        }
      }
    `;

    const result = await hasuraQuery<{ support_faq_by_pk: SupportFAQ | null }>(
      query,
      { variables: { id } }
    );

    return result.support_faq_by_pk;
  } catch (error) {
    console.error("Eroare la obținerea FAQ-ului:", error);
    return null;
  }
}
