import SupportErrorFallback from "@/components/support-error-fallback";
import SupportFAQList from "@/components/support-faq-list";
import { getSupportFAQs } from "@/lib/support-faq";
import { HelpCircle } from "lucide-react";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Suport - Portavio",
  description:
    "Găsiți răspunsuri la întrebările frecvente despre platforma Portavio. Suport pentru gestionarea investițiilor, securitate și funcționalități.",
  keywords: [
    "suport",
    "FAQ",
    "întreb<PERSON>ri frecvente",
    "ajutor",
    "portavio",
    "investiții",
    "asistență",
    "ghid utilizare",
  ],
  openGraph: {
    title: "Suport - Portavio",
    description:
      "Găsiți răspunsuri la întrebările frecvente despre platforma Portavio.",
    type: "website",
  },
};

export default async function SupportPage() {
  try {
    const faqs = await getSupportFAQs(true);

    if (!faqs || faqs.length === 0) {
      return (
        <div className="container mx-auto px-6 py-12 max-w-4xl">
          <div className="text-center mb-12">
            <h1 className="text-3xl font-bold text-foreground mb-4">Suport</h1>
            <p className="text-muted-foreground text-lg">
              Găsiți răspunsuri la întrebările frecvente despre Portavio
            </p>
          </div>

          <div className="text-center py-12">
            <HelpCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">
              Nu sunt disponibile întrebări frecvente în acest moment.
            </p>
          </div>
        </div>
      );
    }

    return (
      <div className="container mx-auto px-6 py-12 max-w-4xl">
        <div className="text-center mb-12">
          <h1 className="text-3xl font-bold text-foreground mb-4">Suport</h1>
          <p className="text-muted-foreground text-lg">
            Găsiți răspunsuri la întrebările frecvente despre Portavio
          </p>
        </div>

        <SupportFAQList faqs={faqs} />
      </div>
    );
  } catch (error) {
    console.error("Eroare la încărcarea paginii de suport:", error);

    return (
      <SupportErrorFallback
        error={error instanceof Error ? error : new Error("Eroare necunoscută")}
      />
    );
  }
}
