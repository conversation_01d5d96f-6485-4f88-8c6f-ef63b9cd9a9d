"use client";

import { useEffect, useState } from "react";

interface BuildInfoProps {
  className?: string;
}

export function BuildInfo({ className = "" }: BuildInfoProps) {
  const [buildInfo, setBuildInfo] = useState<{
    version: string;
    buildTime: string;
  } | null>(null);

  useEffect(() => {
    const loadBuildInfo = async () => {
      try {
        const version = process.env.NEXT_PUBLIC_APP_VERSION!;
        const buildTime = process.env.NEXT_PUBLIC_BUILD_TIME!;

        if (buildTime) {
          const date = new Date(buildTime);
          const formattedTime = date.toLocaleString("ro-RO", {
            year: "numeric",
            month: "2-digit",
            day: "2-digit",
            hour: "2-digit",
            minute: "2-digit",
            hour12: false,
          });

          setBuildInfo({
            version,
            buildTime: formattedTime,
          });
        } else {
          setBuildInfo({
            version,
            buildTime: "dev",
          });
        }
      } catch (error) {
        console.warn("Error loading build info:", error);
        setBuildInfo({
          version: "0.1.0",
          buildTime: "dev",
        });
      }
    };

    loadBuildInfo();
  }, []);

  if (!buildInfo) {
    return null;
  }

  return (
    <div className={`text-xs text-portavio-navy ${className}`}>
      v{buildInfo.version} @ {buildInfo.buildTime}
    </div>
  );
}
