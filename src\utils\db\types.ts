// GraphQL Response Types
export interface GraphQLResponse<T = any> {
  data?: T;
  errors?: GraphQLError[];
  extensions?: Record<string, any>;
}

export interface GraphQLError {
  message: string;
  locations?: Array<{
    line: number;
    column: number;
  }>;
  path?: Array<string | number>;
  extensions?: {
    code?: string;
    path?: string;
    [key: string]: any;
  };
}

// GraphQL Request Types
export interface GraphQLRequest {
  query: string;
  variables?: Record<string, any>;
  operationName?: string;
}

// GraphQL Client Configuration
export interface GraphQLClientConfig {
  endpoint: string;
  headers?: Record<string, string>;
  timeout?: number;
}

// Custom Error Classes
export class GraphQLClientError extends Error {
  public response?: Response;
  public request?: GraphQLRequest;
  public graphQLErrors?: GraphQLError[];

  constructor(
    message: string,
    options?: {
      response?: Response;
      request?: GraphQLRequest;
      graphQLErrors?: GraphQLError[];
    }
  ) {
    super(message);
    this.name = 'GraphQLClientError';
    this.response = options?.response;
    this.request = options?.request;
    this.graphQLErrors = options?.graphQLErrors;
  }
}

export class GraphQLNetworkError extends GraphQLClientError {
  constructor(message: string, options?: { response?: Response; request?: GraphQLRequest }) {
    super(message, options);
    this.name = 'GraphQLNetworkError';
  }
}

export class GraphQLValidationError extends GraphQLClientError {
  constructor(message: string, graphQLErrors: GraphQLError[], options?: { request?: GraphQLRequest }) {
    super(message, { ...options, graphQLErrors });
    this.name = 'GraphQLValidationError';
  }
}

// Utility Types for better TypeScript support
export type Variables = Record<string, any>;

export type QueryOptions<TVariables extends Variables = Variables> = {
  variables?: TVariables;
  operationName?: string;
  headers?: Record<string, string>;
  timeout?: number;
};

export type MutationOptions<TVariables extends Variables = Variables> = QueryOptions<TVariables>;

// Helper type for extracting data from GraphQL responses
export type GraphQLData<T> = T extends GraphQLResponse<infer U> ? U : never;
