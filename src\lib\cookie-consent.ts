import type { CookieConsentStatus } from "@/hooks/use-cookie-consent";

const STORAGE_KEY = "portavio-cookie-consent";

interface CookieConsentData {
  status: CookieConsentStatus;
  timestamp: string;
}

/**
 * Get the current cookie consent status from localStorage
 * This is a utility function for server-side or non-hook usage
 */
export function getCookieConsentStatus(): CookieConsentStatus {
  if (typeof window === "undefined") {
    return null;
  }

  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (stored) {
      const data: CookieConsentData = JSON.parse(stored);
      return data.status;
    }
  } catch (error) {
    console.error("Error reading cookie consent from localStorage:", error);
  }

  return null;
}

/**
 * Check if user has accepted cookies
 */
export function hasCookieConsent(): boolean {
  return getCookieConsentStatus() === "accepted";
}

/**
 * Check if user has refused cookies
 */
export function hasRefusedCookies(): boolean {
  return getCookieConsentStatus() === "refused";
}

/**
 * Check if user has made any choice about cookies
 */
export function hasConsentChoice(): boolean {
  return getCookieConsentStatus() !== null;
}
