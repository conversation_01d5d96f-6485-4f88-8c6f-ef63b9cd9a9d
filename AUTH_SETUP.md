# Better Auth Setup Guide

This guide will help you set up the Better Auth authentication system in your Portavio project.

## 🚀 Quick Setup

### 1. Environment Variables

Create a `.env.local` file in your project root and add the following variables:

```env
# Database Configuration
HASURA_ENDPOINT=https://your-hasura-endpoint.com/v1/graphql
HASURA_SECRET=your-hasura-admin-secret

# Better Auth Configuration
BETTER_AUTH_SECRET=your-secret-key-here-generate-a-random-string
BETTER_AUTH_URL=http://localhost:3000

# Database Connection for Better Auth (PostgreSQL)
DATABASE_URL=postgresql://username:password@localhost:5432/your_database_name
```

**Important:** Generate a secure random string for `BETTER_AUTH_SECRET`. You can use:
```bash
openssl rand -base64 32
```

### 2. Database Setup

Run the SQL script to create the required tables in your PostgreSQL database:

```sql
-- Execute the contents of database/better-auth-schema.sql in your PostgreSQL database
```

The script creates the following tables:
- `user` - Stores user information including email, username, and profile data
- `session` - Manages user sessions and authentication tokens
- `account` - Handles OAuth provider accounts (for future social login)
- `verification` - Manages email verification and password reset tokens

### 3. Dependencies

The following packages have been installed:
- `better-auth` - Main authentication library
- `kysely` - Database query builder
- `pg` - PostgreSQL client
- `@types/pg` - TypeScript types for PostgreSQL

## 📋 Features Implemented

### ✅ Authentication Methods
- **Email + Password** - Users can sign up and sign in with email
- **Username + Password** - Users can also sign in with their username
- **Username Plugin** - Supports both email and username for login

### ✅ Sign Up Form
- Email validation (must be unique)
- Username validation (must be unique, 3-30 characters, alphanumeric + underscore/dots)
- Password validation (minimum 8 characters, must contain uppercase, lowercase, and number)
- Confirm password validation
- Terms & Conditions checkbox (required)
- Romanian error messages

### ✅ Sign In Form
- Accepts either email or username
- Password field
- Automatic detection of email vs username
- Romanian error messages

### ✅ Route Protection
- Middleware-based route protection
- Protected routes: `/dashboard`
- Auth routes: `/auth/signin`, `/auth/signup`
- Automatic redirects based on authentication status

### ✅ Session Management
- 7-day session expiration
- Automatic session refresh
- Secure session handling

### ✅ UI Components
- Responsive sign-up and sign-in forms
- User profile dropdown in header
- Dashboard with user information
- Consistent styling with Tailwind CSS
- Dark/light theme support

## 🛠 Usage

### Authentication Pages
- **Sign Up**: `/auth/signup`
- **Sign In**: `/auth/signin`
- **Dashboard**: `/dashboard` (protected)

### Hooks and Utilities

```typescript
// Use authentication state
import { useAuth } from "@/hooks/use-auth";

const { user, isAuthenticated, isLoading } = useAuth();

// Require authentication for a page
import { useRequireAuth } from "@/hooks/use-auth";

useRequireAuth(); // Redirects to /auth/signin if not authenticated

// Redirect if already authenticated
import { useRedirectIfAuthenticated } from "@/hooks/use-auth";

useRedirectIfAuthenticated(); // Redirects to /dashboard if authenticated
```

### Auth Utilities

```typescript
import { authUtils } from "@/lib/auth-utils";

// Sign up
await authUtils.signUp({
  email: "<EMAIL>",
  username: "username",
  password: "password123"
});

// Sign in (with email or username)
await authUtils.signIn({
  identifier: "<EMAIL>", // or "username"
  password: "password123"
});

// Sign out
await authUtils.signOut();

// Get current session
const session = await authUtils.getSession();
```

## 🔧 Configuration

### Username Validation
- Minimum length: 3 characters
- Maximum length: 30 characters
- Allowed characters: letters, numbers, underscore, dots
- Reserved usernames: admin, root, system, api, www

### Password Requirements
- Minimum 8 characters
- Must contain at least one uppercase letter
- Must contain at least one lowercase letter
- Must contain at least one number

### Session Configuration
- Session expires in 7 days
- Session updates every 24 hours
- Secure cookie settings

## 🚨 Security Features

- **Password Hashing**: Automatic secure password hashing
- **CSRF Protection**: Built-in CSRF protection
- **Session Security**: Secure session management
- **Input Validation**: Server-side validation with Zod schemas
- **SQL Injection Protection**: Parameterized queries via Kysely

## 🎨 Customization

### Styling
The authentication forms use your existing Tailwind CSS configuration and support both light and dark themes.

### Error Messages
All error messages are in Romanian and can be customized in:
- `src/lib/auth-schemas.ts` - Form validation messages
- `src/lib/auth-utils.ts` - Authentication error messages

### Redirects
Default redirects can be customized in:
- `src/middleware.ts` - Route protection redirects
- `src/hooks/use-auth.ts` - Hook-based redirects

## 🧪 Testing

To test the authentication system:

1. Start your development server: `npm run dev`
2. Navigate to `/auth/signup` to create a new account
3. Try signing in with both email and username at `/auth/signin`
4. Access the protected dashboard at `/dashboard`
5. Test the logout functionality

## 🔄 Next Steps

The authentication system is ready for production use. You can extend it by:

1. **Email Verification**: Enable email verification in the auth config
2. **Social Login**: Add OAuth providers (Google, GitHub, etc.)
3. **Password Reset**: Implement password reset functionality
4. **Two-Factor Authentication**: Add 2FA support
5. **User Profile Management**: Create user settings pages

## 📚 Documentation

For more information, refer to:
- [Better Auth Documentation](https://www.better-auth.com/docs)
- [Better Auth Username Plugin](https://www.better-auth.com/docs/plugins/username)
