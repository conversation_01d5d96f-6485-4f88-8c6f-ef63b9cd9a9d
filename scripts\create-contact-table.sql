CREATE TABLE IF NOT EXISTS ptvuser_contact_submissions (
    id SERIAL PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    surname <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    ip_address INET,
    user_agent TEXT,
    status VARCHAR(50) DEFAULT 'new' CHECK (status IN ('new', 'read', 'replied', 'archived')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Indexes for better performance
    CONSTRAINT ptvuser_contact_submissions_email_check CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$')
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_ptvuser_contact_submissions_created_at ON ptvuser_contact_submissions(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_ptvuser_contact_submissions_status ON ptvuser_contact_submissions(status);
CREATE INDEX IF NOT EXISTS idx_ptvuser_contact_submissions_email ON ptvuser_contact_submissions(email);
CREATE INDEX IF NOT EXISTS idx_ptvuser_contact_submissions_ip_address ON ptvuser_contact_submissions(ip_address);

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_ptvuser_contact_submissions_updated_at
    BEFORE UPDATE ON ptvuser_contact_submissions
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Add comments for documentation
COMMENT ON TABLE ptvuser_contact_submissions IS 'Stores contact form submissions from the website';
COMMENT ON COLUMN ptvuser_contact_submissions.id IS 'Auto-incrementing unique identifier for the contact submission';
COMMENT ON COLUMN ptvuser_contact_submissions.name IS 'First name of the person submitting the form';
COMMENT ON COLUMN ptvuser_contact_submissions.surname IS 'Last name of the person submitting the form';
COMMENT ON COLUMN ptvuser_contact_submissions.email IS 'Email address of the person submitting the form';
COMMENT ON COLUMN ptvuser_contact_submissions.message IS 'The message content from the contact form';
COMMENT ON COLUMN ptvuser_contact_submissions.ip_address IS 'IP address of the person submitting the form (for rate limiting)';
COMMENT ON COLUMN ptvuser_contact_submissions.user_agent IS 'Browser user agent string';
COMMENT ON COLUMN ptvuser_contact_submissions.status IS 'Status of the contact submission (new, read, replied, archived)';
COMMENT ON COLUMN ptvuser_contact_submissions.created_at IS 'Timestamp when the submission was created';
COMMENT ON COLUMN ptvuser_contact_submissions.updated_at IS 'Timestamp when the submission was last updated';
