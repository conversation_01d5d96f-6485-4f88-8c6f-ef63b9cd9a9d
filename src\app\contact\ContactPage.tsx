"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { CheckCircle, AlertCircle, Send, Loader2 } from "lucide-react";

const contactFormSchema = z.object({
  name: z
    .string()
    .min(2, "Prenumele trebuie să aibă cel puțin 2 caractere")
    .max(255, "Prenumele este prea lung"),
  surname: z
    .string()
    .min(2, "Numele trebuie să aibă cel puțin 2 caractere")
    .max(255, "Numele este prea lung"),
  email: z
    .string()
    .email("Vă rugăm să introduceți o adresă de email validă")
    .max(255, "Adresa de email este prea lungă"),
  message: z
    .string()
    .min(10, "Mesajul trebuie să aibă cel puțin 10 caractere")
    .max(5000, "Mesajul este prea lung"),
});

type ContactFormData = z.infer<typeof contactFormSchema>;

interface SubmissionState {
  status: "idle" | "loading" | "success" | "error";
  message: string;
  details?: string[];
}

export default function ContactPage() {
  const [submissionState, setSubmissionState] = useState<SubmissionState>({
    status: "idle",
    message: "",
  });

  const form = useForm<ContactFormData>({
    resolver: zodResolver(contactFormSchema),
    defaultValues: {
      name: "",
      surname: "",
      email: "",
      message: "",
    },
  });

  const onSubmit = async (data: ContactFormData) => {
    setSubmissionState({ status: "loading", message: "" });

    try {
      const response = await fetch("/api/contact", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (response.ok) {
        setSubmissionState({
          status: "success",
          message: result.message || "Mesajul a fost trimis cu succes!",
        });
        form.reset();
      } else {
        setSubmissionState({
          status: "error",
          message:
            result.error || "A apărut o eroare. Vă rugăm să încercați din nou.",
          details: result.details,
        });
      }
    } catch (error) {
      console.error("Eroare formular contact:", error);
      setSubmissionState({
        status: "error",
        message:
          "A apărut o eroare de rețea. Vă rugăm să verificați conexiunea și să încercați din nou.",
      });
    }
  };

  const isLoading = submissionState.status === "loading";

  return (
    <div className="container mx-auto px-6 py-12 max-w-4xl">
      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle className="text-3xl font-bold text-center text-foreground">
            Contact
          </CardTitle>
          <p className="text-center text-muted-foreground mt-2">
            Aveți întrebări sau sugestii? Ne-ar plăcea să auzim de la
            dumneavoastră!
          </p>
        </CardHeader>

        <CardContent>
          {submissionState.status === "success" && (
            <div className="mb-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg flex items-start gap-3">
              <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400 mt-0.5 flex-shrink-0" />
              <div>
                <p className="text-green-800 dark:text-green-200 font-medium">
                  Succes!
                </p>
                <p className="text-green-700 dark:text-green-300 text-sm">
                  {submissionState.message}
                </p>
              </div>
            </div>
          )}

          {submissionState.status === "error" && (
            <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg flex items-start gap-3">
              <AlertCircle className="h-5 w-5 text-red-600 dark:text-red-400 mt-0.5 flex-shrink-0" />
              <div>
                <p className="text-red-800 dark:text-red-200 font-medium">
                  Eroare
                </p>
                <p className="text-red-700 dark:text-red-300 text-sm">
                  {submissionState.message}
                </p>
                {submissionState.details &&
                  submissionState.details.length > 0 && (
                    <ul className="mt-2 text-red-700 dark:text-red-300 text-sm list-disc list-inside">
                      {submissionState.details.map((detail, index) => (
                        <li key={index}>{detail}</li>
                      ))}
                    </ul>
                  )}
              </div>
            </div>
          )}

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Prenume</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Introduceți prenumele"
                          disabled={isLoading}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="surname"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Nume</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Introduceți numele"
                          disabled={isLoading}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Adresă de email</FormLabel>
                    <FormControl>
                      <Input
                        type="email"
                        placeholder="<EMAIL>"
                        disabled={isLoading}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="message"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Mesaj</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Scrieți mesajul dumneavoastră aici..."
                        className="min-h-[120px]"
                        disabled={isLoading}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="flex justify-center">
                <Button
                  type="submit"
                  size="lg"
                  disabled={isLoading}
                  className="bg-portavio-orange hover:bg-portavio-orange-hover text-white font-medium px-8 py-3 rounded-lg text-lg min-w-[140px]"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Se trimite...
                    </>
                  ) : (
                    <>
                      <Send className="mr-2 h-4 w-4" />
                      Trimite
                    </>
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}
