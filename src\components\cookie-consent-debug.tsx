"use client";

import { But<PERSON> } from "@/components/ui/button";
import { useCookieConsent } from "@/hooks/use-cookie-consent";

/**
 * Debug component for testing cookie consent functionality used only in development environment
 */
export function CookieConsentDebug() {
  const { consentStatus, clearConsent, hasConsent } = useCookieConsent();

  if (process.env.NODE_ENV === "production") {
    return null;
  }

  return (
    <div className="fixed bottom-4 left-4 bg-card border border-border rounded-lg p-4 shadow-lg z-50 max-w-xs">
      <h3 className="font-semibold text-sm mb-2"><PERSON>ie Consent Debug</h3>
      <div className="text-xs space-y-2">
        <p>
          <strong>Status:</strong> {consentStatus || "No choice made"}
        </p>
        <p>
          <strong>Has Consent:</strong> {hasConsent ? "Yes" : "No"}
        </p>
        <Button
          size="sm"
          variant="outline"
          onClick={clearConsent}
          className="w-full text-xs"
        >
          Clear Consent (Reset Popup)
        </Button>
      </div>
    </div>
  );
}
