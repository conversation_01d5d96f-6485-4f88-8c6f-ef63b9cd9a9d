services:
  portavio-web:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: portavio-web
    ports:
      - "3000:3000"
    restart: unless-stopped
    env_file:
      - .env
    environment:
      - NODE_ENV=production
      - TZ=Europe/Bucharest
  portavio-web-dev:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: portavio-web-dev
    ports:
      - "3001:3000"
    volumes:
      - .:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
    profiles:
      - dev
