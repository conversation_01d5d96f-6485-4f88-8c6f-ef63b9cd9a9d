"use client";

import { useAuth } from "@/hooks/use-auth";
import { authUtils } from "@/lib/auth-utils";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { User, LogOut, Settings } from "lucide-react";
import { useRouter } from "next/navigation";
import Link from "next/link";

interface UserProfileProps {
  isMobile?: boolean;
  onLinkClick?: () => void;
}

export function UserProfile({
  isMobile = false,
  onLinkClick,
}: UserProfileProps) {
  const { user, isAuthenticated, isLoading } = useAuth();
  const router = useRouter();

  const handleSignOut = async () => {
    try {
      await authUtils.signOut();
      router.push("/");
    } catch (error) {
      console.error("Sign out error:", error);
    }
  };

  if (isLoading) {
    return <div className="h-8 w-8 animate-pulse rounded-full bg-muted"></div>;
  }

  if (!isAuthenticated) {
    if (isMobile) {
      return (
        <div className="flex flex-col gap-4">
          <Link
            href="/auth/signin"
            className="text-white hover:text-blue-200 transition-colors py-2 text-center"
            onClick={onLinkClick}
          >
            Loghează-te
          </Link>
          <Link
            href="/auth/signup"
            className="text-white hover:text-blue-200 transition-colors py-2 text-center"
            onClick={onLinkClick}
          >
            Fă primul pas
          </Link>
        </div>
      );
    }

    return (
      <div className="flex items-center gap-4">
        <Link
          href="/auth/signin"
          className="text-white hover:text-blue-200 transition-colors"
        >
          Loghează-te
        </Link>
        <Link
          href="/auth/signup"
          className="text-white hover:text-blue-200 transition-colors"
        >
          Fă primul pas
        </Link>
      </div>
    );
  }

  if (isMobile) {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <button className="flex items-center gap-3 p-2 rounded-lg hover:bg-white/10 transition-colors cursor-pointer">
            <div className="flex h-8 w-8 items-center border-1 border-white justify-center rounded-full bg-portavio-orange hover:bg-portavio-orange-hover text-white flex-shrink-0">
              {user?.username?.[0]?.toUpperCase() ||
                user?.name?.[0]?.toUpperCase() ||
                "U"}
            </div>
            <div className="flex flex-col items-start text-left min-w-0">
              <p className="text-sm font-medium leading-none text-white truncate">
                {user?.name || user?.username}
              </p>
              <p className="text-xs leading-none text-gray-300 truncate">
                {user?.email}
              </p>
            </div>
          </button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-56" align="start" forceMount>
          <DropdownMenuLabel className="font-normal">
            <div className="flex flex-col space-y-1">
              <p className="text-sm font-medium leading-none">
                {user?.name || user?.username}
              </p>
              <p className="text-xs leading-none text-muted-foreground">
                {user?.email}
              </p>
            </div>
          </DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem asChild>
            <Link href="/dashboard" className="cursor-pointer">
              <User className="mr-2 h-4 w-4" />
              <span>Dashboard</span>
            </Link>
          </DropdownMenuItem>
          <DropdownMenuItem disabled>
            <Settings className="mr-2 h-4 w-4" />
            <span>Setări</span>
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={handleSignOut} className="cursor-pointer">
            <LogOut className="mr-2 h-4 w-4" />
            <span>Deconectează-te</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="relative h-8 w-8 rounded-full">
          <div className="flex h-10 w-10 p-4 text-lg border-white items-center justify-center rounded-full bg-portavio-orange hover:bg-portavio-orange-hover text-white">
            {user?.username?.[0]?.toUpperCase() ||
              user?.name?.[0]?.toUpperCase() ||
              "U"}
          </div>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-56" align="end" forceMount>
        <DropdownMenuLabel className="font-normal">
          <div className="flex flex-col space-y-1">
            <p className="text-sm font-medium leading-none">
              {user?.name || user?.username}
            </p>
            <p className="text-xs leading-none text-muted-foreground">
              {user?.email}
            </p>
          </div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem asChild>
          <Link href="/dashboard" className="cursor-pointer">
            <User className="mr-2 h-4 w-4" />
            <span>Dashboard</span>
          </Link>
        </DropdownMenuItem>
        <DropdownMenuItem disabled>
          <Settings className="mr-2 h-4 w-4" />
          <span>Setări</span>
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={handleSignOut} className="cursor-pointer">
          <LogOut className="mr-2 h-4 w-4" />
          <span>Deconectează-te</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
