"use client";

import { Card, CardContent } from "@/components/ui/card";
import { ChevronDown, ChevronRight, HelpCircle } from "lucide-react";
import { useState } from "react";

interface SupportFAQ {
  id: number;
  question: string;
  answer: string;
  is_active: boolean;
  display_order: number;
  created_at: string;
  updated_at: string;
}

interface SupportFAQListProps {
  faqs: SupportFAQ[];
}

export default function SupportFAQList({ faqs }: SupportFAQListProps) {
  const [expandedItems, setExpandedItems] = useState<Set<number>>(new Set());

  const toggleExpanded = (id: number) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(id)) {
      newExpanded.delete(id);
    } else {
      newExpanded.add(id);
    }
    setExpandedItems(newExpanded);
  };

  const isExpanded = (id: number) => expandedItems.has(id);

  if (faqs.length === 0) {
    return (
      <div className="text-center py-12">
        <HelpCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
        <p className="text-muted-foreground">
          Nu sunt disponibile întrebări frecvente în acest moment.
        </p>
      </div>
    );
  }

  return (
    <>
      <div className="space-y-4">
        {faqs.map((faq) => (
          <Card
            key={faq.id}
            className="shadow-sm hover:shadow-md transition-shadow duration-200"
          >
            <CardContent className="p-0">
              <button
                onClick={() => toggleExpanded(faq.id)}
                className="w-full p-6 text-left flex items-center justify-between hover:bg-muted/50 transition-colors duration-200 rounded-lg cursor-pointer"
              >
                <h3 className="text-lg font-semibold text-portavio-navy-light dark:text-white pr-4">
                  {faq.question}
                </h3>
                <div className="flex-shrink-0">
                  {isExpanded(faq.id) ? (
                    <div className="rounded-[50%] p-3 flex justify-center items-center bg-portavio-navy dark:bg-portavio-blue-dark">
                      <ChevronDown className="h-8 w-8 text-white dark:text-white transition-transform duration-200" />
                    </div>
                  ) : (
                    <div className="rounded-[50%] p-3 flex justify-center items-center bg-white shadow-lg">
                      <ChevronRight className="h-8 w-8 text-portavio-navy transition-transform duration-200" />
                    </div>
                  )}
                </div>
              </button>

              {isExpanded(faq.id) && (
                <div className="px-6 pb-6 pt-0">
                  <div className="border-t border-border pt-4">
                    <div
                      className="text-foreground leading-relaxed prose prose-sm max-w-none dark:prose-invert"
                      dangerouslySetInnerHTML={{ __html: faq.answer }}
                    />
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="mt-12 text-center shadow-sm">
        <div className="bg-muted/50 rounded-lg p-6">
          <h3 className="text-lg font-medium text-foreground mb-2">
            Nu ați găsit răspunsul căutat?
          </h3>
          <p className="text-muted-foreground mb-4">
            Contactați-ne și vă vom ajuta cu plăcere!
          </p>
          <a
            href="/contact"
            className="inline-flex items-center gap-2 bg-portavio-orange hover:bg-portavio-orange-hover text-white font-medium px-6 py-3 rounded-lg transition-colors duration-200"
          >
            <HelpCircle className="h-4 w-4" />
            Contactează-ne
          </a>
        </div>
      </div>
    </>
  );
}
