import { createAuthClient } from "better-auth/react";
import { usernameClient } from "better-auth/client/plugins";

export const authClient = createAuthClient({
  baseURL:
    typeof window !== "undefined"
      ? window.location.origin
      : "http://localhost:3000",
  plugins: [usernameClient()],
});

// Export specific methods for convenience
export const { signIn, signUp, signOut, useSession, updateUser, getSession } =
  authClient;
