"use client";

import { Button } from "@/components/ui/button";
import { useCookieConsent } from "@/hooks/use-cookie-consent";
import Link from "next/link";
import { useEffect, useState } from "react";

export function CookieConsent() {
  const { shouldShowPopup, setConsent } = useCookieConsent();
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    if (shouldShowPopup) {
      const timer = setTimeout(() => setIsVisible(true), 100);
      return () => clearTimeout(timer);
    }
  }, [shouldShowPopup]);

  useEffect(() => {
    if (!shouldShowPopup || !isVisible) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        event.preventDefault();
      }
      if (event.key === "Tab") {
        const focusableElements = document.querySelectorAll(
          'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );
        const firstElement = focusableElements[0] as HTMLElement;
        const lastElement = focusableElements[
          focusableElements.length - 1
        ] as HTMLElement;

        if (event.shiftKey && document.activeElement === firstElement) {
          event.preventDefault();
          lastElement?.focus();
        } else if (!event.shiftKey && document.activeElement === lastElement) {
          event.preventDefault();
          firstElement?.focus();
        }
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [shouldShowPopup, isVisible]);

  const handleAccept = () => {
    setConsent("accepted");
    setIsVisible(false);
  };

  const handleRefuse = () => {
    setConsent("refused");
    setIsVisible(false);
  };

  if (!shouldShowPopup) {
    return null;
  }

  return (
    <>
      <div
        className={`
          fixed inset-0 z-[9999] bg-black/60 backdrop-blur-sm transition-opacity duration-300
          ${isVisible ? "opacity-100" : "opacity-0"}
        `}
        style={{ pointerEvents: isVisible ? "auto" : "none" }}
        aria-hidden="true"
      />

      <div
        className={`
          fixed inset-0 z-[10000] flex items-center justify-center p-4 transition-all duration-300
          ${isVisible ? "opacity-100 scale-100" : "opacity-0 scale-95"}
        `}
        style={{ pointerEvents: isVisible ? "auto" : "none" }}
        role="dialog"
        aria-modal="true"
        aria-labelledby="cookie-consent-title"
        aria-describedby="cookie-consent-description"
      >
        <div className="bg-background border border-border rounded-lg shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto animate-in fade-in-0 zoom-in-95 duration-300">
          <div className="p-6 space-y-4">
            <h2
              id="cookie-consent-title"
              className="text-xl font-semibold text-foreground"
            >
              Înainte de a continua: pe scurt cu privire la prelucrarea datelor
              dvs.
            </h2>

            <div
              id="cookie-consent-description"
              className="text-sm text-muted-foreground leading-relaxed space-y-3"
            >
              <p>
                <strong>Prelucrăm</strong> datele dvs. pe site-ul nostru web,
                utilizând diverse tehnici de prelucrare (inclusiv cookie-uri),
                în măsura în care acest lucru este necesar din punct de vedere
                tehnic pentru a afișa site-ul web și funcțiile acestuia. În
                plus, prelucrăm datele dvs. de urmărire, în scopul unor setări
                ale site-ului nostru web care să corespundă preferințelor
                dumneavoastră, pentru a crea statistici pseudonime sau pentru a
                afișa conținut personalizat (publicitar) de către noi sau de
                către terți, numai dacă ne dați consimțământul dvs. alegând
                opțiunea „Sunt de acord&quot; de mai jos.
              </p>

              <p>
                Aceasta include, de asemenea, transferurile de date către țări
                din afara UE care nu asigură un nivel adecvat de protecție a
                datelor cu caracter personal. Prin opțiunea „Refuz&quot; puteți
                permite doar utilizarea tehnicilor necesare pentru afișarea
                site-ului web și a funcțiilor acestuia.
              </p>

              <p>
                Puteți găsi mai multe informații, inclusiv cu privire la dreptul
                dvs. de retragere gratuită a consimțământului în orice moment,
                în{" "}
                <Link
                  href="/confidentiality"
                  className="text-primary hover:text-primary/80 underline"
                >
                  Politica noastră de confidențialitate
                </Link>
                .
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-3 pt-4">
              <Button
                variant="outline"
                onClick={handleRefuse}
                className="flex-1 sm:flex-none sm:order-1 order-2"
              >
                Refuz
              </Button>
              <Button
                onClick={handleAccept}
                className="flex-1 bg-portavio-orange hover:bg-portavio-orange-hover text-white sm:order-2 order-1"
              >
                Accept
              </Button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
