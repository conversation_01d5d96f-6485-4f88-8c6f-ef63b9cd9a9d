"use client";

import { useAuth, useRequireAuth } from "@/hooks/use-auth";
import { authUtils } from "@/lib/auth-utils";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useRouter } from "next/navigation";

export default function DashboardPage() {
  const { user, isLoading } = useAuth();
  const router = useRouter();
  
  // Require authentication for this page
  const { isAuthenticated } = useRequireAuth();

  const handleSignOut = async () => {
    try {
      await authUtils.signOut();
      router.push("/");
    } catch (error) {
      console.error("Sign out error:", error);
    }
  };

  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-portavio-orange mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Se încarcă...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null; // useRequireAuth will handle redirect
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="flex justify-between items-center mb-8">
            <div>
              <h1 className="text-3xl font-bold text-foreground">Dashboard</h1>
              <p className="text-muted-foreground mt-1">
                Bine ai venit, {user?.username || user?.name}!
              </p>
            </div>
            <Button onClick={handleSignOut} variant="outline">
              Deconectează-te
            </Button>
          </div>

          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle>Profilul tău</CardTitle>
                <CardDescription>
                  Informații despre contul tău
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div>
                    <span className="font-medium">Email:</span> {user?.email}
                  </div>
                  <div>
                    <span className="font-medium">Username:</span> {user?.username}
                  </div>
                  <div>
                    <span className="font-medium">Nume:</span> {user?.name || "Nu este setat"}
                  </div>
                  <div>
                    <span className="font-medium">Cont creat:</span>{" "}
                    {user?.createdAt ? new Date(user.createdAt).toLocaleDateString("ro-RO") : "N/A"}
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Portofoliu</CardTitle>
                <CardDescription>
                  Vizualizează și gestionează portofoliul tău
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Funcționalitatea de portofoliu va fi implementată în curând.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Setări</CardTitle>
                <CardDescription>
                  Configurează preferințele tale
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Setările vor fi disponibile în curând.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
