// import { NextRequest, NextResponse } from "next/server";
// import { auth } from "@/lib/auth";

// export async function middleware(request: NextRequest) {
//   // const { pathname } = request.nextUrl;
//   // // Define protected routes
//   // const protectedRoutes = ["/dashboard"];
//   // const authRoutes = ["/auth/signin", "/auth/signup"];
//   // // Check if the current path is protected
//   // const isProtectedRoute = protectedRoutes.some((route) =>
//   //   pathname.startsWith(route)
//   // );
//   // // Check if the current path is an auth route
//   // const isAuthRoute = authRoutes.some((route) => pathname.startsWith(route));
//   // try {
//   //   // Get session from better-auth
//   //   const session = await auth.api.getSession({
//   //     headers: request.headers,
//   //   });
//   //   // If user is authenticated and trying to access auth pages, redirect to dashboard
//   //   if (session && isAuthRoute) {
//   //     return NextResponse.redirect(new URL("/dashboard", request.url));
//   //   }
//   //   // If user is not authenticated and trying to access protected routes, redirect to signin
//   //   if (!session && isProtectedRoute) {
//   //     return NextResponse.redirect(new URL("/auth/signin", request.url));
//   //   }
//   //   return NextResponse.next();
//   // } catch (error) {
//   //   console.error("Middleware error:", error);
//   //   // If there's an error and user is trying to access protected route, redirect to signin
//   //   if (isProtectedRoute) {
//   //     return NextResponse.redirect(new URL("/auth/signin", request.url));
//   //   }
//   //   return NextResponse.next();
//   // }
// }

// export const config = {
//   matcher: [
//     /*
//      * Match all request paths except for the ones starting with:
//      * - api (API routes)
//      * - _next/static (static files)
//      * - _next/image (image optimization files)
//      * - favicon.ico (favicon file)
//      */
//     "/((?!api|_next/static|_next/image|favicon.ico).*)",
//   ],
// };
