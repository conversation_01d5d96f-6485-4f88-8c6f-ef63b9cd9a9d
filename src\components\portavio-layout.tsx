"use client";

import { usePathname } from "next/navigation";
import { Header } from "@/components/header";
import { Footer } from "@/components/footer";

interface PortavioLayoutProps {
  children: React.ReactNode;
}

export function PortavioLayout({ children }: PortavioLayoutProps) {
  const pathname = usePathname();
  const isLandingPage = pathname === "/";

  if (isLandingPage) {
    return <>{children}</>;
  }

  return (
    <div className="min-h-screen flex flex-col bg-background theme-transition">
      <Header variant="page" />
      <main className="flex-1 pt-20">{children}</main>
      <Footer />
    </div>
  );
}
