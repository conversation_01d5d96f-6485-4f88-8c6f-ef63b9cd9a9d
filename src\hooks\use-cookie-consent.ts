"use client";

import { useState, useEffect } from "react";

export type CookieConsentStatus = "accepted" | "refused" | null;

interface CookieConsentData {
  status: CookieConsentStatus;
  timestamp: string;
}

const STORAGE_KEY = "portavio-cookie-consent";

export function useCookieConsent() {
  const [consentStatus, setConsentStatus] = useState<CookieConsentStatus>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored) {
        const data: CookieConsentData = JSON.parse(stored);
        setConsentStatus(data.status);
      }
    } catch (error) {
      console.error("Error reading cookie consent from localStorage:", error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const setConsent = (status: CookieConsentStatus) => {
    if (!status) return;

    const data: CookieConsentData = {
      status,
      timestamp: new Date().toISOString(),
    };

    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(data));
      setConsentStatus(status);
    } catch (error) {
      console.error("Error saving cookie consent to localStorage:", error);
    }
  };

  const clearConsent = () => {
    try {
      localStorage.removeItem(STORAGE_KEY);
      setConsentStatus(null);
      window.location.reload();
    } catch (error) {
      console.error("Error clearing cookie consent from localStorage:", error);
    }
  };

  return {
    consentStatus,
    isLoading,
    setConsent,
    clearConsent,
    hasConsent: consentStatus !== null,
    shouldShowPopup: !isLoading && consentStatus === null,
  };
}
