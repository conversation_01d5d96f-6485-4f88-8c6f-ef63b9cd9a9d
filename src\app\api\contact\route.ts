import {
  buildInsertMutation,
  hasuraMutation,
  isHasuraConstraintError,
} from "@/utils/db";
import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";

const rateLimitMap = new Map<string, { count: number; resetTime: number }>();

const RATE_LIMIT_WINDOW = 15 * 60 * 1000; // 15 minutes
const RATE_LIMIT_MAX_REQUESTS = 3; // Max 3 submissions per 15 minutes per IP

const contactFormSchema = z.object({
  name: z
    .string({ required_error: "Numele este obligatoriu" })
    .min(2, "Numele trebuie să aibă cel puțin 2 caractere")
    .max(255, "Numele este prea lung (maxim 255 caractere)")
    .trim(),
  surname: z
    .string({ required_error: "Prenumele este obligatoriu" })
    .min(2, "Prenumele trebuie să aibă cel puțin 2 caractere")
    .max(255, "Prenumele este prea lung (maxim 255 caractere)")
    .trim(),
  email: z
    .string({ required_error: "Adresa de email este obligatorie" })
    .email("Vă rugăm să introduceți o adresă de email validă")
    .max(255, "Adresa de email este prea lungă (maxim 255 caractere)")
    .trim()
    .toLowerCase(),
  message: z
    .string({ required_error: "Mesajul este obligatoriu" })
    .min(10, "Mesajul trebuie să aibă cel puțin 10 caractere")
    .max(5000, "Mesajul este prea lung (maxim 5000 caractere)")
    .trim(),
});

type ContactFormData = z.infer<typeof contactFormSchema>;

interface ContactSubmissionResponse {
  insert_ptvuser_contact_submissions: {
    returning: Array<{
      id: number;
      name: string;
      surname: string;
      email: string;
      message: string;
      created_at: string;
    }>;
  };
}

function getClientIP(request: NextRequest): string {
  // Try to get real IP from various headers
  const forwarded = request.headers.get("x-forwarded-for");
  const realIP = request.headers.get("x-real-ip");
  const cfConnectingIP = request.headers.get("cf-connecting-ip");

  if (forwarded) {
    return forwarded.split(",")[0].trim();
  }
  if (realIP) {
    return realIP;
  }
  if (cfConnectingIP) {
    return cfConnectingIP;
  }

  return "unknown";
}

function isRateLimited(ip: string): boolean {
  const now = Date.now();
  const userLimit = rateLimitMap.get(ip);

  if (!userLimit || now > userLimit.resetTime) {
    rateLimitMap.set(ip, {
      count: 1,
      resetTime: now + RATE_LIMIT_WINDOW,
    });
    return false;
  }

  if (userLimit.count >= RATE_LIMIT_MAX_REQUESTS) {
    return true;
  }

  userLimit.count++;
  return false;
}

function validateContactForm(data: unknown): {
  isValid: boolean;
  errors: string[];
  data?: ContactFormData;
} {
  try {
    const validatedData = contactFormSchema.parse(data);
    return {
      isValid: true,
      errors: [],
      data: validatedData,
    };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errors = error.errors.map((err) => err.message);
      return {
        isValid: false,
        errors,
      };
    }
    return {
      isValid: false,
      errors: ["Eroare de validare necunoscută"],
    };
  }
}

export async function POST(request: NextRequest) {
  try {
    const clientIP = getClientIP(request);

    if (isRateLimited(clientIP)) {
      return NextResponse.json(
        {
          error:
            "Prea multe cereri. Vă rugăm să așteptați înainte de a trimite un alt mesaj.",
          code: "RATE_LIMITED",
        },
        { status: 429 }
      );
    }

    const body = await request.json();

    const validation = validateContactForm(body);
    if (!validation.isValid) {
      return NextResponse.json(
        {
          error: "Validarea a eșuat",
          details: validation.errors,
          code: "VALIDATION_ERROR",
        },
        { status: 400 }
      );
    }

    const { name, surname, email, message } = validation.data!;

    const userAgent = request.headers.get("user-agent") || "unknown";

    const contactData = {
      name,
      surname,
      email,
      message,
      ip_address: clientIP,
      user_agent: userAgent,
      status: "new",
    };

    const mutation = buildInsertMutation("ptvuser_contact_submissions", [
      "id",
      "name",
      "surname",
      "email",
      "message",
      "created_at",
    ]);

    const result = await hasuraMutation<ContactSubmissionResponse>(mutation, {
      variables: {
        objects: [contactData],
      },
    });

    const submission = result.insert_ptvuser_contact_submissions.returning[0];

    return NextResponse.json({
      success: true,
      message:
        "Mesajul dumneavoastră a fost trimis cu succes! Vă vom contacta în curând.",
      submissionId: submission.id,
    });
  } catch (error) {
    console.error("Eroare trimitere formular contact:", error);

    if (isHasuraConstraintError(error)) {
      return NextResponse.json(
        {
          error:
            "O restricție de bază de date a fost încălcată. Vă rugăm să verificați datele introduse.",
          code: "CONSTRAINT_ERROR",
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        error:
          "A apărut o eroare neașteptată. Vă rugăm să încercați din nou mai târziu.",
        code: "INTERNAL_ERROR",
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({ error: "Metodă nepermisă" }, { status: 405 });
}

export async function PUT() {
  return NextResponse.json({ error: "Metodă nepermisă" }, { status: 405 });
}

export async function DELETE() {
  return NextResponse.json({ error: "Metodă nepermisă" }, { status: 405 });
}
