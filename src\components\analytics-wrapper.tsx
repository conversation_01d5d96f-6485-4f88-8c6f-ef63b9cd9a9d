"use client";

import { useCookieConsent } from "@/hooks/use-cookie-consent";
import { useEffect } from "react";

interface AnalyticsWrapperProps {
  children: React.ReactNode;
}

/**
 * Example component showing how to conditionally load analytics
 * based on cookie consent status
 */
export function AnalyticsWrapper({ children }: AnalyticsWrapperProps) {
  const { consentStatus, isLoading } = useCookieConsent();

  useEffect(() => {
    if (isLoading) return;

    if (consentStatus === "accepted") {
      // User has accepted cookies - safe to load analytics
      console.log("✅ Cookie consent accepted - Analytics can be loaded");

      // Example: Load Google Analytics
      // loadGoogleAnalytics();

      // Example: Load other tracking scripts
      // loadFacebookPixel();
      // loadHotjar();
    } else if (consentStatus === "refused") {
      // User has refused cookies - only essential functionality
      console.log("❌ Cookie consent refused - Only essential cookies allowed");

      // Example: Remove any existing tracking
      // removeTrackingScripts();
    } else {
      // No consent yet - popup should be showing
      console.log("⏳ No cookie consent yet - Waiting for user choice");
    }
  }, [consentStatus, isLoading]);

  return <>{children}</>;
}

// // Example utility functions (implement as needed)
// function loadGoogleAnalytics() {
//   // Implementation for loading Google Analytics
//   // Only call this after user has accepted cookies
// }

// function loadFacebookPixel() {
//   // Implementation for loading Facebook Pixel
//   // Only call this after user has accepted cookies
// }

// function loadHotjar() {
//   // Implementation for loading Hotjar
//   // Only call this after user has accepted cookies
// }

// function removeTrackingScripts() {
//   // Implementation for removing tracking scripts
//   // Call this if user refuses cookies
// }
