"use client";

import { HelpCircle, RefreshCw } from "lucide-react";
import { Button } from "@/components/ui/button";
import Link from "next/link";

interface SupportErrorFallbackProps {
  error?: Error;
  reset?: () => void;
}

export default function SupportErrorFallback({
  error,
  reset,
}: SupportErrorFallbackProps) {
  return (
    <div className="container mx-auto px-6 py-12 max-w-4xl">
      <div className="text-center mb-12">
        <h1 className="text-3xl font-bold text-foreground mb-4">Suport</h1>
        <p className="text-muted-foreground text-lg">
          Găsiți răspunsuri la întrebările frecvente despre Portavio
        </p>
      </div>

      <div className="text-center py-12">
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-8 max-w-md mx-auto">
          <HelpCircle className="h-12 w-12 text-red-600 dark:text-red-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-red-800 dark:text-red-200 mb-2">
            Eroare la încărcarea paginii
          </h3>
          <p className="text-red-700 dark:text-red-300 text-sm mb-6">
            {error?.message ||
              "A apărut o eroare neașteptată la încărcarea întrebărilor frecvente."}
          </p>

          <div className="space-y-3">
            {reset && (
              <Button
                onClick={reset}
                variant="outline"
                className="w-full border-red-300 text-red-700 hover:bg-red-50 dark:border-red-700 dark:text-red-300 dark:hover:bg-red-900/30"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Încearcă din nou
              </Button>
            )}

            <Button
              onClick={() => window.location.reload()}
              className="w-full bg-red-600 hover:bg-red-700 text-white"
            >
              Reîncarcă pagina
            </Button>

            <Link href="/contact" className="inline-block w-full">
              <Button
                variant="ghost"
                className="w-full text-muted-foreground hover:text-foreground"
              >
                Contactează suportul
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
