# GraphQL Client for Hasura

A fully typed GraphQL client built with plain fetch for seamless integration with Hasura. This client provides comprehensive error handling, TypeScript support, and convenient utilities for common GraphQL operations.

## Features

- ✅ **Fully Typed**: Complete TypeScript support with generic types
- ✅ **Error Handling**: Comprehensive error handling with custom error classes
- ✅ **Hasura Integration**: Built specifically for <PERSON><PERSON> with admin secret support
- ✅ **No Dependencies**: Uses only native fetch API
- ✅ **Query Builders**: Convenient utilities for building common queries
- ✅ **Timeout Support**: Configurable request timeouts
- ✅ **Network Error Handling**: Proper handling of network failures

## Setup

### Environment Variables

Add the following environment variables to your `.env` file:

```env
HASURA_ENDPOINT=https://your-hasura-endpoint.com/v1/graphql
HASURA_SECRET=your-hasura-admin-secret
```

### Basic Usage

```typescript
import { hasuraQuery, hasuraMutation } from "@/utils/db";

// Simple query
const users = await hasuraQuery<{ users: User[] }>(
  `query {
    users {
      id
      name
      email
    }
  }`
);

// Query with variables
const user = await hasuraQuery<{ users_by_pk: User }>(
  `query GetUser($id: uuid!) {
    users_by_pk(id: $id) {
      id
      name
      email
    }
  }`,
  { variables: { id: "user-id" } }
);

// Mutation
const newUser = await hasuraMutation<{ insert_users: { returning: User[] } }>(
  `mutation CreateUser($object: users_insert_input!) {
    insert_users(objects: [$object]) {
      returning {
        id
        name
        email
      }
    }
  }`,
  { variables: { object: { name: "John", email: "<EMAIL>" } } }
);
```

## API Reference

### Core Functions

#### `hasuraQuery<TData, TVariables>(query, options?)`

Execute a GraphQL query against Hasura.

```typescript
const result = await hasuraQuery<{ users: User[] }>(
  "query { users { id name email } }"
);
```

#### `hasuraMutation<TData, TVariables>(mutation, options?)`

Execute a GraphQL mutation against Hasura.

```typescript
const result = await hasuraMutation<{ insert_users: { returning: User[] } }>(
  "mutation { insert_users(objects: $objects) { returning { id } } }",
  { variables: { objects: [{ name: "John" }] } }
);
```

### Query Builders

#### `buildSelectQuery(table, fields, where?, orderBy?, limit?, offset?)`

Build a select query for a table.

```typescript
const query = buildSelectQuery(
  "users",
  ["id", "name", "email"],
  { active: { _eq: true } },
  { created_at: "desc" },
  10,
  0
);
```

#### `buildInsertMutation(table, fields, onConflict?)`

Build an insert mutation for a table.

```typescript
const mutation = buildInsertMutation("users", ["id", "name", "email"], {
  constraint: "users_email_key",
  update_columns: ["name"],
});
```

#### `buildUpdateMutation(table, fields, pkColumns?)`

Build an update mutation for a table.

```typescript
const mutation = buildUpdateMutation("users", ["id", "name", "email"]);
```

#### `buildDeleteMutation(table, fields?)`

Build a delete mutation for a table.

```typescript
const mutation = buildDeleteMutation("users", ["id"]);
```

### Error Handling

The client provides several error classes for different scenarios:

#### `GraphQLClientError`

Base error class for all GraphQL client errors.

#### `GraphQLNetworkError`

Thrown when network requests fail (connection issues, timeouts, HTTP errors).

#### `GraphQLValidationError`

Thrown when GraphQL returns validation errors.

### Error Checking Utilities

```typescript
import {
  isHasuraPermissionError,
  isHasuraConstraintError,
  isHasuraValidationError,
} from "@/utils/db";

try {
  await hasuraMutation(mutation, { variables });
} catch (error) {
  if (isHasuraPermissionError(error)) {
    console.log("Permission denied");
  } else if (isHasuraConstraintError(error)) {
    console.log("Constraint violation");
  } else if (isHasuraValidationError(error)) {
    console.log("Validation failed");
  }
}
```

## Advanced Usage

### Custom Headers

```typescript
await hasuraQuery(query, {
  headers: {
    "x-hasura-role": "user",
    "x-hasura-user-id": "user-123",
  },
});
```

### Timeout Configuration

```typescript
await hasuraQuery(query, {
  timeout: 10000, // 10 seconds
});
```

### Using the Raw Client

```typescript
import { hasuraClient } from "@/utils/db";

// Direct client usage
const result = await hasuraClient.request({
  query: "query { users { id } }",
  variables: {},
  operationName: "GetUsers",
});
```

## Best Practices

1. **Type Safety**: Always provide TypeScript types for your queries and mutations
2. **Error Handling**: Wrap operations in try-catch blocks and handle specific error types
3. **Variables**: Use GraphQL variables instead of string interpolation
4. **Timeouts**: Set appropriate timeouts for long-running operations
5. **Caching**: Consider implementing caching for frequently accessed data
