import { NextResponse } from 'next/server';
import { hasuraQuery, hasuraMutation } from '@/utils/db';

export async function POST(request: Request) {
  try {
    const { query, variables, operation } = await request.json();
    
    if (operation === 'query') {
      const result = await hasuraQuery(query, { variables });
      return NextResponse.json(result);
    } else if (operation === 'mutation') {
      const result = await hasuraMutation(query, { variables });
      return NextResponse.json(result);
    } else {
      return NextResponse.json(
        { error: 'Invalid operation type' },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error('GraphQL API error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}